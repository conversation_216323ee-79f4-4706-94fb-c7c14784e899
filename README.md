# Coding Buddy

A secure, agentic, multi-agent, IDE-style coding assistant for modern AI-powered development.

## Project Structure

- `backend/` – FastAPI app, agents, security, project file ops
- `frontend/` – React app, components, services
- `projects/` – User project directories (isolated)

## Tech Stack

- **Backend:** <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, OpenRouter
- **Frontend:** <PERSON><PERSON>, Monaco Editor, xterm.js, React Diff Viewer, etc.

## Quick Start

1. Install backend dependencies:
   ```bash
   cd backend
   pip install -r requirements.txt
   ```
2. Install frontend dependencies:
   ```bash
   cd frontend
   npm install
   ```
3. Start backend:
   ```bash
   uvicorn main:app --reload
   ```
4. Start frontend (in another terminal):
   ```bash
   npm start
   ```

## Security & Approval Workflow
- All file writes and commands require user approval.
- Project isolation and command whitelisting enforced.

---

See `CODING_BUDDY_IMPLEMENTATION_PLAN.md` for full details. 