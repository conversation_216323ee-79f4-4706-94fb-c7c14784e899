from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import List, Dict

from ..services import openrouter_client

router = APIRouter(prefix="/api/chat", tags=["chat"])

class Message(BaseModel):
    role: str  # "user" or "assistant"
    content: str

class ChatRequest(BaseModel):
    messages: List[Message]

class ChatResponse(BaseModel):
    content: str

@router.post("/completion", response_model=ChatResponse)
async def chat_completion(req: ChatRequest):
    try:
        messages = [m.dict() for m in req.messages]
        content = await openrouter_client.chat_completion(messages)
        return ChatResponse(content=content)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e)) 