from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import List, Dict, Optional

from ..projects import file_ops

router = APIRouter(prefix="/api/projects", tags=["projects"])


class FileContent(BaseModel):
    content: str
    approved: Optional[bool] = True  # future flag for approval workflow


@router.get("/{project_id}/list/{path:path}")
async def list_directory(project_id: str, path: str = "") -> List[Dict]:
    try:
        return file_ops.list_dir(project_id, path)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/{project_id}/files/{path:path}")
async def read_file(project_id: str, path: str):
    try:
        content = file_ops.read_file(project_id, path)
        return {"path": path, "content": content}
    except (ValueError, FileNotFoundError) as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/{project_id}/files/{path:path}")
async def write_file(project_id: str, path: str, file: FileContent):
    try:
        file_ops.write_file(project_id, path, file.content, approved=file.approved)
        return {"status": "success"}
    except NotImplementedError as e:
        raise HTTPException(status_code=501, detail=str(e))
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e)) 