"""
Project file operations (read, write, list, etc.)
- All operations are restricted to the project root for isolation.
"""

from pathlib import Path
from typing import List, Dict
import os

from ..config import PROJECTS_ROOT
from ..security.sandbox import validate_project_path


def _resolve_project_path(project_id: str, rel_path: str = "") -> Path:
    """Return absolute path within the project root, ensuring isolation."""
    root = Path(PROJECTS_ROOT) / project_id
    root.mkdir(parents=True, exist_ok=True)
    abs_path = root / rel_path
    # Normalize path to remove .. or .
    abs_path = abs_path.resolve()
    try:
        abs_path.relative_to(root.resolve())
    except ValueError:
        raise ValueError("Invalid path: outside project root")
    if not validate_project_path(str(abs_path), str(root)):
        raise ValueError("Path validation failed")
    return abs_path


def list_dir(project_id: str, rel_path: str = "") -> List[Dict]:
    path = _resolve_project_path(project_id, rel_path)
    if not path.exists():
        return []
    entries = []
    for p in path.iterdir():
        entries.append({"name": p.name, "is_dir": p.is_dir()})
    return entries


def read_file(project_id: str, rel_path: str) -> str:
    path = _resolve_project_path(project_id, rel_path)
    if not path.is_file():
        raise FileNotFoundError("File not found")
    with open(path, "r", encoding="utf-8") as f:
        return f.read()


def write_file(project_id: str, rel_path: str, content: str, *, approved: bool = True) -> None:
    if not approved:
        # TODO: Store proposal for approval workflow
        raise NotImplementedError("Approval workflow pending")
    path = _resolve_project_path(project_id, rel_path)
    path.parent.mkdir(parents=True, exist_ok=True)
    with open(path, "w", encoding="utf-8") as f:
        f.write(content) 