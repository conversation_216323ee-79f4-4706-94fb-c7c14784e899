import React, { useState } from 'react';
import FileExplorer from './components/FileExplorer';
import EditorTabs from './components/EditorTabs';
import ChatPanel from './components/ChatPanel';

export default function App() {
  const [selectedFile, setSelectedFile] = useState(null);

  return (
    <div style={{ display: 'flex', height: '100vh' }}>
      <FileExplorer onSelectFile={setSelectedFile} />
      <EditorTabs selectedFile={selectedFile} />
      <div style={{ width: '30%', borderLeft: '1px solid #ccc' }}>
        <ChatPanel />
      </div>
    </div>
  );
} 