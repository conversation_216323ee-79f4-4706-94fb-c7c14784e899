import React, { useEffect, useState } from 'react';
import { listDir } from '../services/api';

export default function FileExplorer({ onSelectFile }) {
  const [path, setPath] = useState(''); // current dir path
  const [entries, setEntries] = useState([]);
  const [error, setError] = useState(null);

  useEffect(() => {
    loadEntries(path);
  }, [path]);

  async function loadEntries(relPath) {
    try {
      const data = await listDir(relPath);
      setEntries(data);
      setError(null);
    } catch (e) {
      setError(e.message);
    }
  }

  function handleClick(entry) {
    if (entry.is_dir) {
      setPath(path ? `${path}/${entry.name}` : entry.name);
    } else {
      const filePath = path ? `${path}/${entry.name}` : entry.name;
      onSelectFile(filePath);
    }
  }

  function handleBack() {
    if (!path) return;
    const parts = path.split('/');
    parts.pop();
    setPath(parts.join('/'));
  }

  return (
    <div style={{ width: '250px', borderRight: '1px solid #ccc', overflowY: 'auto' }}>
      <div style={{ padding: '8px', borderBottom: '1px solid #ccc', fontWeight: 'bold' }}>
        File Explorer
        {path && (
          <button style={{ marginLeft: 8 }} onClick={handleBack}>
            Up
          </button>
        )}
      </div>
      {error && <div style={{ color: 'red' }}>{error}</div>}
      <ul style={{ listStyle: 'none', padding: 0 }}>
        {entries.map((entry) => (
          <li
            key={entry.name}
            style={{ cursor: 'pointer', padding: '4px 8px' }}
            onClick={() => handleClick(entry)}
          >
            {entry.is_dir ? '📁' : '📄'} {entry.name}
          </li>
        ))}
        {entries.length === 0 && <li style={{ padding: '4px 8px' }}>(empty)</li>}
      </ul>
    </div>
  );
} 