import React from 'react';
import NiceModal, { useModal } from '@ebay/nice-modal-react';
import Diff<PERSON>ie<PERSON> from 'react-diff-viewer-continued';

function SaveDiffModal({ oldText, newText, filePath }) {
  const modal = useModal();
  return (
    <div
      style={{
        position: 'fixed',
        inset: 0,
        background: 'rgba(0,0,0,0.4)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 1000,
      }}
    >
      <div
        style={{
          padding: 16,
          background: '#fff',
          width: '80vw',
          maxHeight: '80vh',
          overflow: 'auto',
          borderRadius: 4,
        }}
      >
        <h3 style={{ marginTop: 0 }}>Confirm Save – {filePath}</h3>
        <DiffViewer
          oldValue={oldText}
          newValue={newText}
          splitView={true}
          showDiffOnly={false}
          styles={{
            variables: {
              light: {
                diffViewerBackground: '#f7f7f7',
              },
            },
          }}
        />
        <div style={{ marginTop: 16, textAlign: 'right' }}>
          <button onClick={() => modal.resolve(true)} style={{ marginRight: 8 }}>
            Save
          </button>
          <button onClick={() => modal.remove()}>Cancel</button>
        </div>
      </div>
    </div>
  );
}

export default NiceModal.create(SaveDiffModal); 