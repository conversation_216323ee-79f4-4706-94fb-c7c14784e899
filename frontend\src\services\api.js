const BASE_URL = process.env.REACT_APP_BACKEND_URL || 'http://localhost:8000';
const PROJECT_ID = 'demo'; // temporary default

async function request(url, options = {}) {
  const res = await fetch(url, options);
  if (!res.ok) {
    const data = await res.json().catch(() => ({}));
    throw new Error(data.detail || `Request failed: ${res.status}`);
  }
  return res.json();
}

export async function listDir(path = '') {
  const url = `${BASE_URL}/api/projects/${PROJECT_ID}/list/${encodeURIComponent(path)}`;
  return request(url);
}

export async function readFile(path) {
  const url = `${BASE_URL}/api/projects/${PROJECT_ID}/files/${encodeURIComponent(path)}`;
  return request(url);
}

export async function writeFile(path, content, approved = true) {
  const url = `${BASE_URL}/api/projects/${PROJECT_ID}/files/${encodeURIComponent(path)}`;
  return request(url, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ content, approved }),
  });
} 